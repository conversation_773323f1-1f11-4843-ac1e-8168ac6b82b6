import argparse
import json
import pandas as pd
import requests
from openpyxl import load_workbook
import time  # <-- aggiunto per il timing

def read_excel_column_to_json(excel_file, column_name, max_rows=200):
    """
    Read a column from an Excel file and transform it into JSON arrays with pagination.

    Args:
        excel_file (str): Path to the Excel file
        column_name (str): Column letter or name to read
        max_rows (int): Maximum number of rows per JSON array (pagination)

    Returns:
        list: List of JSON objects, each containing at most max_rows rows
        str: Header value of the column
    """
    # Load the Excel file
    df = pd.read_excel(excel_file)

    # Convert column name to proper format if it's a letter
    if len(column_name) == 1 and column_name.isalpha():
        # If it's a single letter, try to convert it to the corresponding column name
        try:
            column_name = df.columns[ord(column_name.upper()) - ord('A')]
        except IndexError:
            raise ValueError(f"Column {column_name} not found in the Excel file")

    # Check if the column exists
    if column_name not in df.columns:
        raise ValueError(f"Column {column_name} not found in the Excel file")

    # Get the header
    header = column_name

    # Get the column data (excluding header)
    column_data = df[column_name].fillna("").astype(str)

    # Create JSON arrays with pagination
    json_arrays = []
    for i in range(0, len(column_data), max_rows):
        chunk = column_data.iloc[i:i+max_rows]
        json_obj = {
            "column": column_name,
            "content": {str(j+1+i): value for j, value in enumerate(chunk)}
        }
        json_arrays.append(json_obj)

    return json_arrays, header

def submit_to_gpt(json_obj, prompt, api_key, endpoint):
    """
    Submit a JSON object to Azure GPT-4o-mini with a fixed prompt.

    Args:
        json_obj (dict): JSON object to submit
        prompt (str): Fixed prompt to use
        api_key (str): Azure API key
        endpoint (str): Azure endpoint URL

    Returns:
        dict: Response from GPT
    """
    start_call = time.time()
    print(f"[LOG] Invio richiesta a OpenAI con payload di {len(json.dumps(json_obj))} caratteri...")
    # Prepare the payload for the API request
    payload = {
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": json.dumps(json_obj)}
        ],
        "model": "gpt-4.1-mini"
    }

    # Set up the headers
    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }

    # Make the API request
    response = requests.post(
        f"{endpoint}/openai/deployments/gpt-4.1-mini/chat/completions?api-version=2025-01-01-preview",
        headers=headers,
        json=payload
    )
    duration = time.time() - start_call
    print(f"[LOG] Risposta ricevuta da OpenAI in {duration:.2f} secondi.")
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()["choices"][0]["message"]["content"]
    else:
        raise Exception(f"API request failed with status code {response.status_code}: {response.text}")

def write_results_to_excel(excel_file, results, output_column, header):
    """
    Write the results to a new column in the Excel file.

    Args:
        excel_file (str): Path to the Excel file
        results (list): List of results to write
        output_column (str): Column letter or name to write to
        header (str): Header value for the output column
    """
    # Load the workbook and get the active sheet
    workbook = load_workbook(excel_file)
    sheet = workbook.active

    # Determine the column index
    if len(output_column) == 1 and output_column.isalpha():
        col_idx = ord(output_column.upper()) - ord('A') + 1
    else:
        # If it's not a single letter, we'll need to find its index
        df = pd.read_excel(excel_file)
        if output_column in df.columns:
            col_idx = df.columns.get_loc(output_column) + 1
        else:
            # If the column doesn't exist, create it
            col_idx = len(df.columns) + 1

    # Write the header first
    header_text = f"{header}" if header else "Translated"
    sheet.cell(row=1, column=col_idx, value=header_text)

    # Write the results to the Excel file, starting from row 2 (after header)
    for i, result in enumerate(results):
        try:
            # Parse the result as JSON to extract the values
            result_json = json.loads(result)

            # Write each value to the corresponding row (add 1 for header row)
            for row_idx, value in result_json.items():
                sheet.cell(row=int(row_idx)+1, column=col_idx, value=value)
        except json.JSONDecodeError:
            # If the result is not valid JSON, write it as is
            sheet.cell(row=i+2, column=col_idx, value=result)  # +2 to account for header and 0-indexing

    # Save the workbook
    workbook.save(excel_file)

def main():
    start_script = time.time()  # <--- aggiungi questa riga per inizializzare il timer
    default_prompt = """
        You are a professional translator specializing in technical documentation for industrial equipment.
        You will be provided with product descriptions for professional laundry machines and spare parts in Japanese, in particular for TOSEI products.
        Translate each description into clear, accurate English while preserving technical terminology.
        Double check the meaning of the single words and phrases to ensure accuracy with the context.

        IMPORTANT: You must format your response as a valid JSON object where the keys are the row numbers and the values are the translated English text. For example:
        {"1": "30kg Industrial Washing Machine", "2": "Stainless Steel Dryer with Steam Function", "3": "Commercial Ironing System"}

        Do not include any explanations or notes outside of this JSON structure.
    """
    parser = argparse.ArgumentParser(description="Process Excel file, create JSON arrays, submit to GPT, and write results back to Excel.")
    parser.add_argument("excel_file", help="Path to the Excel file")
    parser.add_argument("column_name", help="Column letter or name to read")
    parser.add_argument("output_column", help="Column letter or name to write results to")
    parser.add_argument("--max_rows", type=int, default=200, help="Maximum number of rows per JSON array (pagination)")
    parser.add_argument("--prompt", default=default_prompt, help="Fixed prompt to use with GPT")
    parser.add_argument("--api_key", required=True, help="Azure API key")
    parser.add_argument("--endpoint", required=True, help="Azure endpoint URL")
    parser.add_argument("--output_header", help="Custom header for output column (defaults to 'Translated [original_header]')")

    args = parser.parse_args()

    # Read the Excel column to JSON
    json_arrays, original_header = read_excel_column_to_json(args.excel_file, args.column_name, args.max_rows)

    # Use custom header if provided
    header = args.output_header if args.output_header else original_header

    # Submit each JSON array to GPT and collect the responses
    responses = []
    for idx, json_obj in enumerate(json_arrays):
        print(f"[LOG] Elaborazione batch {idx+1}/{len(json_arrays)}")
        response = submit_to_gpt(json_obj, args.prompt, args.api_key, args.endpoint)
        responses.append(response)

    # Write the results to the Excel file
    write_results_to_excel(args.excel_file, responses, args.output_column, header)

    total_duration = time.time() - start_script
    print(f"Processed {len(json_arrays)} JSON arrays and wrote results to column {args.output_column} in {args.excel_file}")
    print(f"[LOG] Script completato in {total_duration:.2f} secondi.")

if __name__ == "__main__":
    main()